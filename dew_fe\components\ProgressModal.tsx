import Colors from "@/constants/Colors";
import { BlurView } from 'expo-blur';
import React, { useCallback, useEffect, useState } from "react";
import {
  Animated,
  Dimensions,
  Easing,
  Modal,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from "react-native";
import { Bar<PERSON>hart } from "react-native-gifted-charts";
import { supabase } from "@/lib/supabase";
import { useAuth } from "@/hooks/useAuth";

interface ProgressModalProps {
  visible: boolean;
  onClose: () => void;
}

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface MoodData {
  date: string;
  average: number;
  count: number;
  moods: Array<{ label: string; value: number; emoji: string }>;
}

interface ChartDataPoint {
  value: number;
  label: string;
  frontColor: string;
}

const ProgressModal = ({ visible, onClose }: ProgressModalProps) => {
  const { userId } = useAuth();
  const [slideAnim] = useState(new Animated.Value(screenHeight));
  const [fadeAnim] = useState(new Animated.Value(0));
  const [scaleAnim] = useState(new Animated.Value(0.9));
  const [chartAnim] = useState(new Animated.Value(0));
  
  // State for mood data
  const [moodData, setMoodData] = useState<MoodData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const [time, setTime] = useState<"daily" | "monthly" | "yearly">("daily");

  // Color palette for mood values (1-10 scale)
  const getMoodColor = (value: number): string => {
    const colors = [
      '#dc2626', // 1 - Very sad (red)
      '#ea580c', // 2 - Sad (orange-red)
      '#f59e0b', // 3 - Low (amber)
      '#eab308', // 4 - Below average (yellow)
      '#84cc16', // 5 - Neutral (lime)
      '#22c55e', // 6 - Good (green)
      '#16a34a', // 7 - Very good (green)
      '#15803d', // 8 - Great (darker green)
      '#166534', // 9 - Excellent (very dark green)
      '#14532d', // 10 - Amazing (darkest green)
    ];
    
    // Clamp value between 1-10 and get corresponding color
    const index = Math.min(Math.max(Math.round(value), 1), 10) - 1;
    return colors[index];
  };

  // Function to fetch and log mood data
  const fetchMoodData = useCallback(async () => {
    if (!userId) {
      console.log("❌ MOOD FETCH: No user ID available");
      return;
    }

    try {
      // Check current Supabase session first
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      console.log("🔐 FETCH SESSION:", session?.user?.id);
      console.log("🔐 FETCH SESSION ERROR:", sessionError);
      console.log("� FETCH USER ID FROM HOOK:", userId);

      if (!session?.user?.id) {
        console.log("❌ MOOD FETCH: No valid session");
        return;
      }

      console.log("�🔍 FETCHING MOOD DATA for user:", userId);
      console.log("🔍 USING SESSION USER:", session.user.id);

      // Try fetching with session user ID first
      const { data: allMoods, error: allError } = await supabase
        .from("mood_logs")
        .select("*")
        .eq("user_id", session.user.id)
        .order("logged_date", { ascending: false });

      if (allError) {
        console.error("❌ MOOD FETCH ERROR:", allError);
        console.error("❌ ERROR CODE:", allError.code);
        console.error("❌ ERROR MESSAGE:", allError.message);
        return;
      }

      console.log("📊 ALL MOOD LOGS:", allMoods);
      console.log("📈 MOOD COUNT:", allMoods?.length || 0);

      if (!allMoods || allMoods.length === 0) {
        console.log("📭 NO MOOD DATA FOUND - Table might be empty or RLS blocking access");
        return;
      }

      // Get mood data for the last 7 days
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      const sevenDaysAgoStr = sevenDaysAgo.toISOString().split("T")[0];

      const { data: recentMoods, error: recentError } = await supabase
        .from("mood_logs")
        .select("*")
        .eq("user_id", userId)
        .gte("logged_date", sevenDaysAgoStr)
        .order("logged_date", { ascending: true });

      if (recentError) {
        console.error("❌ RECENT MOOD FETCH ERROR:", recentError);
        return;
      }

      console.log("📅 RECENT MOOD LOGS (Last 7 days):", recentMoods);

      // Group by date and calculate averages
      const moodsByDate = recentMoods?.reduce((acc: Record<string, any[]>, mood: any) => {
        const date = mood.logged_date;
        if (!acc[date]) {
          acc[date] = [];
        }
        acc[date].push(mood);
        return acc;
      }, {} as Record<string, any[]>);

      console.log("📊 MOODS GROUPED BY DATE:", moodsByDate);

      // Calculate daily averages
      const dailyAverages = Object.entries(moodsByDate || {}).map(([date, moods]: [string, any[]]) => {
        const average = moods.reduce((sum: number, mood: any) => sum + mood.mood_value, 0) / moods.length;
        return {
          date,
          average: Math.round(average),
          count: moods.length,
          moods: moods.map((m: any) => ({ label: m.mood_label, value: m.mood_value, emoji: m.mood_emoji }))
        };
      });

      console.log("📈 DAILY MOOD AVERAGES:", dailyAverages);

    } catch (error) {
      console.error("❌ MOOD FETCH UNEXPECTED ERROR:", error);
    }
  }, [userId]);

  useEffect(() => {
    if (visible) {
      // Fetch mood data when modal opens
      fetchMoodData();

      // Entrance animation
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 600,
          easing: Easing.out(Easing.cubic),
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 400,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 600,
          easing: Easing.out(Easing.back(1.2)),
          useNativeDriver: true,
        }),
      ]).start(() => {
        // Chart animation after modal appears
        Animated.timing(chartAnim, {
          toValue: 1,
          duration: 800,
          easing: Easing.out(Easing.cubic),
          useNativeDriver: true,
        }).start();
      });
    } else {
      // Exit animation
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: screenHeight,
          duration: 400,
          easing: Easing.in(Easing.cubic),
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 0.9,
          duration: 400,
          useNativeDriver: true,
        }),
        Animated.timing(chartAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible, fetchMoodData, slideAnim, fadeAnim, scaleAnim, chartAnim]);

  const handleTimeChange = (newTime: "daily" | "monthly" | "yearly") => {
    // Animate chart out and in
    Animated.sequence([
      Animated.timing(chartAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(chartAnim, {
        toValue: 1,
        duration: 400,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
    ]).start();
    
    setTime(newTime);
  };

  const getCurrentData = () => {
    switch (time) {
      case "daily":
        return datadaily;
      case "monthly":
        return dataMonthly;
      case "yearly":
        return dataYearly;
      default:
        return datadaily;
    }
  };

  const getStatsText = () => {
    const data = getCurrentData();
    const total = data.reduce((sum, item) => sum + item.value, 0);
    const average = Math.round(total / data.length);
    const highest = Math.max(...data.map(item => item.value));
    
    return { total, average, highest };
  };

  const { total, average, highest } = getStatsText();

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={onClose}
    >
      <Animated.View 
        style={[
          styles.overlay,
          {
            opacity: fadeAnim,
          }
        ]}
      >
        <BlurView intensity={20} style={StyleSheet.absoluteFill}>
          <TouchableOpacity 
            style={styles.backdrop}
            activeOpacity={1}
            onPress={onClose}
          />
        </BlurView>
        
        <Animated.View
          style={[
            styles.modalContainer,
            {
              transform: [
                { translateY: slideAnim },
                { scale: scaleAnim }
              ]
            }
          ]}
        >
          <View style={styles.modalContent}>
            {/* Header */}
            <View style={styles.header}>
              <View style={styles.headerContent}>
                <Text style={styles.title}>📊 Mood Progress</Text>
                <Text style={styles.subtitle}>Track your emotional journey</Text>
              </View>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <TouchableOpacity
                  onPress={fetchMoodData}
                  style={[styles.closeButton, { marginRight: 8, backgroundColor: Colors.brand.primary }]}
                >
                  <View style={styles.closeButtonBackground}>
                    <Text style={styles.closeButtonText}>🔄</Text>
                  </View>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={onClose}
                  style={styles.closeButton}
                >
                  <View style={styles.closeButtonBackground}>
                    <Text style={styles.closeButtonText}>✕</Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>

            {/* Stats Cards */}
            <View style={styles.statsContainer}>
              <View style={[styles.statCard, { backgroundColor: '#22c55e20' }]}>
                <Text style={styles.statValue}>{total}</Text>
                <Text style={styles.statLabel}>Total Logs</Text>
              </View>
              <View style={[styles.statCard, { backgroundColor: '#3b82f620' }]}>
                <Text style={styles.statValue}>{average}</Text>
                <Text style={styles.statLabel}>Average</Text>
              </View>
              <View style={[styles.statCard, { backgroundColor: '#f59e0b20' }]}>
                <Text style={styles.statValue}>{highest}</Text>
                <Text style={styles.statLabel}>Peak</Text>
              </View>
            </View>

            {/* Time Period Selector */}
            <View style={styles.selectorContainer}>
              <Text style={styles.selectorLabel}>Time Period</Text>
              <View style={styles.selectorButtons}>
                {(['daily', 'monthly', 'yearly'] as const).map((period) => (
                  <TouchableOpacity
                    key={period}
                    style={[
                      styles.selectorButton,
                      time === period && styles.selectorButtonActive
                    ]}
                    onPress={() => handleTimeChange(period)}
                    activeOpacity={0.7}
                  >
                    <Text style={[
                      styles.selectorButtonText,
                      time === period && styles.selectorButtonTextActive
                    ]}>
                      {period.charAt(0).toUpperCase() + period.slice(1)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Chart */}
            <Animated.View 
              style={[
                styles.chartContainer,
                {
                  opacity: chartAnim,
                  transform: [
                    {
                      translateY: chartAnim.interpolate({
                        inputRange: [0, 1],
                        outputRange: [20, 0],
                      }),
                    },
                  ],
                }
              ]}
            >
              <BarChart
                data={getCurrentData()}
                barWidth={time === "monthly" ? 18 : 20}
                spacing={time === "daily" ? 24 : time === "monthly" ? 10 : 34}
                hideRules
                xAxisThickness={0}
                yAxisThickness={0}
                hideYAxisText
                barBorderRadius={8}
                xAxisLabelsAtBottom={true}
                xAxisLabelsHeight={20}
                xAxisLabelsVerticalShift={0}
                xAxisLabelTextStyle={styles.chartLabelText}
                animationDuration={800}
                isAnimated
              />
            </Animated.View>

            {/* Motivational Quote */}
            <View style={styles.quoteContainer}>
              <View style={styles.quoteBackground}>
                <Text style={styles.quoteText}>
                  &ldquo;Progress, not perfection. Every mood logged is a step forward. 🌟&rdquo;
                </Text>
              </View>
            </View>
          </View>
        </Animated.View>
      </Animated.View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  backdrop: {
    flex: 1,
  },
  modalContainer: {
    maxHeight: screenHeight * 0.85,
  },
  modalContent: {
    borderTopLeftRadius: 32,
    borderTopRightRadius: 32,
    padding: 24,
    paddingBottom: 40,
    backgroundColor: Colors.background.primary,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -10 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  headerContent: {
    flex: 1,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.text.primary,
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    color: Colors.text.secondary,
    opacity: 0.8,
  },
  closeButton: {
    marginLeft: 16,
  },
  closeButtonBackground: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.background.elevated,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    color: Colors.text.primary,
    fontSize: 18,
    fontWeight: 'bold',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  statCard: {
    flex: 1,
    marginHorizontal: 4,
    padding: 16,
    borderRadius: 16,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#3f3f4620',
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.text.primary,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: Colors.text.secondary,
    opacity: 0.7,
  },
  selectorContainer: {
    marginBottom: 24,
  },
  selectorLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 12,
  },
  selectorButtons: {
    flexDirection: 'row',
    backgroundColor: Colors.background.secondary,
    borderRadius: 12,
    padding: 4,
  },
  selectorButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectorButtonActive: {
    backgroundColor: Colors.brand.primary,
  },
  selectorButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.text.secondary,
  },
  selectorButtonTextActive: {
    color: Colors.text.primary,
    fontWeight: '600',
  },
  chartContainer: {
    height: 200,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
    backgroundColor: Colors.background.secondary,
    borderRadius: 16,
    padding: 16,
  },
  chartLabelText: {
    color: Colors.text.secondary,
    fontSize: 12,
  },
  quoteContainer: {
    marginTop: 8,
  },
  quoteBackground: {
    padding: 16,
    borderRadius: 16,
    alignItems: 'center',
    backgroundColor: Colors.background.secondary,
    borderWidth: 1,
    borderColor: '#3f3f4620',
  },
  quoteText: {
    fontSize: 14,
    color: Colors.text.secondary,
    textAlign: 'center',
    fontStyle: 'italic',
    lineHeight: 20,
  },
});

export default ProgressModal;
